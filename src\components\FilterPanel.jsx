import { Search, Filter } from 'lucide-react'

const FilterPanel = ({ 
  selectedCategory, 
  onCategoryChange, 
  searchTerm, 
  onSearchChange 
}) => {
  const categories = [
    { id: 'all', name: '<PERSON><PERSON><PERSON><PERSON>', count: 0 },
    { id: 'drinks', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', count: 0 },
    { id: 'snacks', name: 'Atıştırmalıklar', count: 0 },
    { id: 'breakfast', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>lı<PERSON>', count: 0 },
    { id: 'dairy', name: '<PERSON>üt Ürünleri', count: 0 },
    { id: 'fruits', name: '<PERSON><PERSON><PERSON> & Sebze', count: 0 },
    { id: 'meat', name: '<PERSON><PERSON> & Tavuk', count: 0 },
    { id: 'bakery', name: '<PERSON><PERSON><PERSON><PERSON><PERSON> Ürünleri', count: 0 }
  ]

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 sticky top-20">
      <div className="flex items-center space-x-2 mb-6">
        <Filter className="h-5 w-5 text-primary-500" />
        <h3 className="text-lg font-semibold text-gray-900">Filtreler</h3>
      </div>

      {/* Arama Kutusu */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Ürün Ara
        </label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Ürün adı veya açıklama..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Kategori Filtreleri */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Kategoriler
        </label>
        <div className="space-y-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => onCategoryChange(category.id)}
              className={`w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-primary-100 text-primary-700 border border-primary-200'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="flex items-center justify-between">
                <span>{category.name}</span>
                {category.count > 0 && (
                  <span className="bg-gray-200 text-gray-600 px-2 py-1 rounded-full text-xs">
                    {category.count}
                  </span>
                )}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Fiyat Aralığı (Gelecekte eklenebilir) */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Fiyat Aralığı
        </label>
        <div className="text-sm text-gray-500">
          <p>Bu özellik yakında eklenecek</p>
        </div>
      </div>

      {/* Filtreleri Temizle */}
      {(selectedCategory !== 'all' || searchTerm) && (
        <div className="mt-6 pt-6 border-t border-gray-200">
          <button
            onClick={() => {
              onCategoryChange('all')
              onSearchChange('')
            }}
            className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
          >
            Filtreleri Temizle
          </button>
        </div>
      )}
    </div>
  )
}

export default FilterPanel 