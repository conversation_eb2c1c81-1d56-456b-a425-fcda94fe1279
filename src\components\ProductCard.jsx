import { Tag, Eye } from 'lucide-react'

const ProductCard = ({ product, onClick }) => {
  const { name, description, price, image, discount, category, inStock } = product

  // Kategori adını Türkçe'ye çevir
  const getCategoryName = (category) => {
    const categoryNames = {
      'drinks': 'İçecekler',
      'snacks': 'Atıştırmalıklar',
      'breakfast': 'Kah<PERSON>t<PERSON>lık',
      'dairy': 'Süt Ürünleri',
      'fruits': 'Meyve & Sebze',
      'meat': 'Et & Tavuk',
      'bakery': 'Fırın Ürünleri'
    }
    return categoryNames[category] || category
  }

  // İndirimli fiyatı hesapla
  const discountedPrice = discount ? price * (1 - discount / 100) : price

  return (
    <div className="card group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
      {/* Ürün Görseli */}
      <div className="relative aspect-square overflow-hidden bg-gray-100">
        <img
          src={image || '/placeholder-product.jpg'}
          alt={name}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          onError={(e) => {
            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjhGOUZBIi8+CjxwYXRoIGQ9Ik0xNTAgMTUwQzE2Ni41NjkgMTUwIDE4MCAxMzYuNTY5IDE4MCAxMjBDMTgwIDEwMy40MzEgMTY2LjU2OSA5MCAxNTAgOTBDMTMzLjQzMSA5MCAxMjAgMTAzLjQzMSAxMjAgMTIwQzEyMCAxMzYuNTY5IDEzMy40MzEgMTUwIDE1MCAxNTBaIiBmaWxsPSIjQzRDNUM3Ii8+CjxwYXRoIGQ9Ik0xNTAgMTgwQzE2Ni41NjkgMTgwIDE4MCAxNjYuNTY5IDE4MCAxNTBDMTgwIDEzMy40MzEgMTY2LjU2OSAxMjAgMTUwIDEyMEMxMzMuNDMxIDEyMCAxMjAgMTMzLjQzMSAxMjAgMTUwQzEyMCAxNjYuNTY5IDEzMy40MzEgMTgwIDE1MCAxODBaIiBmaWxsPSIjQzRDNUM3Ii8+CjxwYXRoIGQ9Ik0xNTAgMjEwQzE2Ni41NjkgMjEwIDE4MCAxOTYuNTY5IDE4MCAxODBDMTgwIDE2My40MzEgMTY2LjU2OSAxNTAgMTUwIDE1MEMxMzMuNDMxIDE1MCAxMjAgMTYzLjQzMSAxMjAgMTgwQzEyMCAxOTYuNTY5IDEzMy40MzEgMjEwIDE1MCAyMTBaIiBmaWxsPSIjQzRDNUM3Ii8+Cjwvc3ZnPgo='
          }}
        />
        
        {/* İndirim Badge */}
        {discount && (
          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
            %{discount} İndirim
          </div>
        )}
        
        {/* Stok Durumu */}
        {!inStock && (
          <div className="absolute top-2 right-2 bg-gray-500 text-white px-2 py-1 rounded-full text-xs font-bold">
            Stokta Yok
          </div>
        )}
        
        {/* Kategori Badge */}
        <div className="absolute bottom-2 left-2">
          <span className="bg-primary-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
            <Tag className="h-3 w-3" />
            <span>{getCategoryName(category)}</span>
          </span>
        </div>
      </div>

      {/* Ürün Bilgileri */}
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors">
          {name}
        </h3>
        
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {description}
        </p>
        
        {/* Fiyat */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            {discount ? (
              <>
                <span className="text-lg font-bold text-primary-600">
                  {discountedPrice.toFixed(2)} TL
                </span>
                <span className="text-sm text-gray-500 line-through">
                  {price.toFixed(2)} TL
                </span>
              </>
            ) : (
              <span className="text-lg font-bold text-primary-600">
                {price.toFixed(2)} TL
              </span>
            )}
          </div>
        </div>
        
        {/* Ürünü İncele Butonu */}
        <button
          onClick={onClick}
          disabled={!inStock}
          className={`w-full flex items-center justify-center space-x-2 py-2 px-4 rounded-lg font-medium transition-colors ${
            inStock
              ? 'bg-primary-500 text-white hover:bg-primary-600'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          <Eye className="h-4 w-4" />
          <span>{inStock ? 'Ürünü İncele' : 'Stokta Yok'}</span>
        </button>
      </div>
    </div>
  )
}

export default ProductCard 