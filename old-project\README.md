# Market Vitrini - Modern Market Web Sitesi

Macrocenter benzeri modern ve profesyonel bir market vitrin web sitesi. HTML, CSS ve JavaScript kullanılarak geliştirilmiştir.

## 🚀 Özellikler

### Vitrin Özellikleri
- **Modern ve Responsive Tasarım**: Mobil uyumlu, modern arayüz
- **Kategori Filtreleme**: İçecekler, Atıştırmalıklar, Ka<PERSON><PERSON><PERSON>k, Süt Ürünleri, Meyve & Sebze
- **Ürün <PERSON>lar<PERSON>**: <PERSON>r<PERSON><PERSON> ad<PERSON>, açıklaması, fiyatı ve görseli
- **Smooth Scroll**: Yumuşak sayfa geçişleri
- **Mobil Menü**: Hamburger menü ile mobil uyumluluk

### Admin Panel Özellikleri
- **Güvenli Giriş**: E-posta ve şifre ile doğrulama
- **<PERSON>rün Yönetim<PERSON>**: Ekle<PERSON>, d<PERSON><PERSON><PERSON>e, silme
- **Dashboard**: İstatistik<PERSON> ve genel bakış
- **LocalStorage**: <PERSON><PERSON><PERSON><PERSON> tarayıcıda saklanması

## 📁 Proje <PERSON>

```
Market-Vitrini/
├── index.html              # Ana sayfa
├── admin.html              # Admin paneli
├── css/
│   ├── style.css           # Ana sayfa stilleri
│   └── admin.css           # Admin panel stilleri
├── js/
│   ├── main.js             # Ana sayfa JavaScript
│   └── admin.js            # Admin panel JavaScript
├── images/                 # Görseller klasörü
│   └── placeholder.jpg     # Varsayılan görsel
└── README.md               # Proje dokümantasyonu
```

## 🛠️ Kurulum ve Kullanım

### 1. Projeyi İndirin
```bash
git clone [proje-url]
cd Market-Vitrini
```

### 2. Dosyaları Açın
- `index.html` dosyasını tarayıcıda açın (ana sayfa)
- `admin.html` dosyasını açın (admin paneli)

### 3. Admin Girişi
- **E-posta**: <EMAIL>
- **Şifre**: admin123

## 🎨 Tasarım Özellikleri

### Renk Paleti
- **Ana Renk**: #667eea (Mavi)
- **İkincil Renk**: #764ba2 (Mor)
- **Başarı**: #27ae60 (Yeşil)
- **Hata**: #e74c3c (Kırmızı)
- **Uyarı**: #f39c12 (Turuncu)

### Tipografi
- **Font**: Segoe UI, Tahoma, Geneva, Verdana, sans-serif
- **Başlıklar**: Bold, 2.5rem
- **Metin**: Normal, 1rem

## 📱 Responsive Tasarım

### Breakpoint'ler
- **Desktop**: 1200px+
- **Tablet**: 768px - 1199px
- **Mobil**: 480px - 767px
- **Küçük Mobil**: 480px altı

### Mobil Özellikler
- Hamburger menü
- Responsive grid sistemi
- Touch-friendly butonlar
- Optimize edilmiş görseller

## 🔧 Teknik Detaylar

### JavaScript Özellikleri
- **ES6+ Syntax**: Modern JavaScript kullanımı
- **LocalStorage**: Veri saklama
- **Event Handling**: Kullanıcı etkileşimleri
- **DOM Manipulation**: Dinamik içerik güncelleme

### CSS Özellikleri
- **Flexbox & Grid**: Modern layout sistemleri
- **CSS Variables**: Tutarlı renk yönetimi
- **Animations**: Smooth geçişler
- **Media Queries**: Responsive tasarım

## 🚀 Gelecek Geliştirmeler

### Planlanan Özellikler
- [ ] Firebase entegrasyonu
- [ ] Gerçek veritabanı bağlantısı
- [ ] Ürün arama fonksiyonu
- [ ] Gelişmiş filtreleme
- [ ] Ürün detay sayfaları
- [ ] Çoklu dil desteği
- [ ] SEO optimizasyonu

### Güvenlik İyileştirmeleri
- [ ] JWT token authentication
- [ ] Şifre hashleme
- [ ] Rate limiting
- [ ] Input validation

## 📝 Kod Yapısı

### Modüler Yapı
- Her dosya tek bir sorumluluğa sahip
- Fonksiyonlar mantıklı gruplandırılmış
- Yorum satırları ile açıklamalar
- Temiz ve okunabilir kod

### Best Practices
- Semantic HTML kullanımı
- CSS class naming conventions
- JavaScript error handling
- Cross-browser compatibility

## 🤝 Katkıda Bulunma

1. Projeyi fork edin
2. Feature branch oluşturun (`git checkout -b feature/AmazingFeature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add some AmazingFeature'`)
4. Branch'inizi push edin (`git push origin feature/AmazingFeature`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 👨‍💻 Geliştirici

Bu proje geliştirici asistanı tarafından oluşturulmuştur.

---

**Not**: Bu proje eğitim amaçlı geliştirilmiştir. Gerçek kullanım için güvenlik önlemleri alınmalıdır. 