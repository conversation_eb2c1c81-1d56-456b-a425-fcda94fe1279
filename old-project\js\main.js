// Ür<PERSON>n verileri - gerçek projede bu veriler Firebase'den gelecek
let products = [
    {
        id: 1,
        name: "<PERSON><PERSON>",
        description: "Günlük taze çiğ süt, 1 litre",
        price: "15.90 TL",
        category: "dairy",
        image: "images/milk.jpg"
    },
    {
        id: 2,
        name: "<PERSON>k<PERSON><PERSON>",
        description: "Taze köy ekmeği, 500gr",
        price: "8.50 TL",
        category: "breakfast",
        image: "images/bread.jpg"
    },
    {
        id: 3,
        name: "<PERSON><PERSON>",
        description: "<PERSON><PERSON>ya elması, 1kg",
        price: "12.90 TL",
        category: "fruits",
        image: "images/apple.jpg"
    },
    {
        id: 4,
        name: "<PERSON>",
        description: "Doğal kaynak suyu, 5 litre",
        price: "18.50 TL",
        category: "drinks",
        image: "images/water.jpg"
    },
    {
        id: 5,
        name: "Cips",
        description: "Patates cipsi, 150gr",
        price: "22.90 TL",
        category: "snacks",
        image: "images/chips.jpg"
    },
    {
        id: 6,
        name: "Peynir",
        description: "Beyaz peynir, 500gr",
        price: "45.90 TL",
        category: "dairy",
        image: "images/cheese.jpg"
    },
    {
        id: 7,
        name: "Domates",
        description: "Taze domates, 1kg",
        price: "16.90 TL",
        category: "fruits",
        image: "images/tomato.jpg"
    },
    {
        id: 8,
        name: "Meyve Suyu",
        description: "Portakal suyu, 1 litre",
        price: "25.90 TL",
        category: "drinks",
        image: "images/juice.jpg"
    },
    {
        id: 9,
        name: "Çikolata",
        description: "Sütlü çikolata, 100gr",
        price: "18.90 TL",
        category: "snacks",
        image: "images/chocolate.jpg"
    },
    {
        id: 10,
        name: "Yumurta",
        description: "Taze yumurta, 15 adet",
        price: "32.90 TL",
        category: "breakfast",
        image: "images/eggs.jpg"
    },
    {
        id: 11,
        name: "Yoğurt",
        description: "Tam yağlı yoğurt, 1kg",
        price: "28.90 TL",
        category: "dairy",
        image: "images/yogurt.jpg"
    },
    {
        id: 12,
        name: "Muz",
        description: "Taze muz, 1kg",
        price: "24.90 TL",
        category: "fruits",
        image: "images/banana.jpg"
    }
];

// Sayfa yüklendiğinde çalışacak fonksiyonlar
document.addEventListener('DOMContentLoaded', function() {
    // Ürünleri yükle
    loadProducts('all');
    
    // Kategori filtrelerini ayarla
    setupCategoryFilters();
    
    // Mobil menü için hamburger menü
    setupMobileMenu();
    
    // Smooth scroll için linkleri ayarla
    setupSmoothScroll();
});

// Ürünleri yükleme fonksiyonu
function loadProducts(category) {
    const productsGrid = document.getElementById('productsGrid');
    let filteredProducts = products;
    
    // Kategori filtresi uygula
    if (category !== 'all') {
        filteredProducts = products.filter(product => product.category === category);
    }
    
    // Ürünleri HTML'e dönüştür
    productsGrid.innerHTML = filteredProducts.map(product => `
        <div class="product-card" data-category="${product.category}">
            <img src="${product.image}" alt="${product.name}" class="product-image" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjI1MCIgdmlld0JveD0iMCAwIDMwMCAyNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjUwIiBmaWxsPSIjRjhGOUZBIi8+CjxwYXRoIGQ9Ik0xNTAgMTI1QzE2Ni41NjkgMTI1IDE4MCAxMTEuNTY5IDE4MCA5NUMxODAgNzguNDMxMSAxNjYuNTY5IDY1IDE1MCA2NUMxMzMuNDMxIDY1IDEyMCA3OC40MzExIDEyMCA5NUMxMjAgMTExLjU2OSAxMzMuNDMxIDEyNSAxNTAgMTI1WiIgZmlsbD0iI0M0QzVDNyIvPgo8cGF0aCBkPSJNMTUwIDE3NUMxNjYuNTY5IDE3NSAxODAgMTYxLjU2OSAxODAgMTQ1QzE4MCAxMjguNDMxIDE2Ni41NjkgMTE1IDE1MCAxMTVDMTMzLjQzMSAxMTUgMTIwIDEyOC40MzEgMTIwIDE0NUMxMjAgMTYxLjU2OSAxMzMuNDMxIDE3NSAxNTAgMTc1WiIgZmlsbD0iI0M0QzVDNyIvPgo8cGF0aCBkPSJNMTUwIDIyNUMxNjYuNTY5IDIyNSAxODAgMjExLjU2OSAxODAgMTk1QzE4MCAxNzguNDMxIDE2Ni41NjkgMTY1IDE1MCAxNjVDMTMzLjQzMSAxNjUgMTIwIDE3OC40MzEgMTIwIDE5NUMxMjAgMjExLjU2OSAxMzMuNDMxIDIyNSAxNTAgMjI1WiIgZmlsbD0iI0M0QzVDNyIvPgo8L3N2Zz4K'">
            <div class="product-info">
                <h3 class="product-title">${product.name}</h3>
                <p class="product-description">${product.description}</p>
                <div class="product-price">${product.price}</div>
                <span class="product-category">${getCategoryName(product.category)}</span>
            </div>
        </div>
    `).join('');
}

// Kategori adını Türkçe'ye çevir
function getCategoryName(category) {
    const categoryNames = {
        'drinks': 'İçecekler',
        'snacks': 'Atıştırmalıklar',
        'breakfast': 'Kahvaltılık',
        'dairy': 'Süt Ürünleri',
        'fruits': 'Meyve & Sebze'
    };
    return categoryNames[category] || category;
}

// Kategori filtrelerini ayarla
function setupCategoryFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Aktif buton stilini güncelle
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Ürünleri filtrele
            const category = this.getAttribute('data-category');
            loadProducts(category);
        });
    });
}

// Mobil menü ayarları
function setupMobileMenu() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    hamburger.addEventListener('click', function() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });
    
    // Menü linklerine tıklandığında menüyü kapat
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function() {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });
}

// Smooth scroll ayarları
function setupSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Navbar scroll efekti
window.addEventListener('scroll', function() {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
        header.style.backdropFilter = 'blur(10px)';
    } else {
        header.style.backgroundColor = '#fff';
        header.style.backdropFilter = 'none';
    }
});

// Aktif menü linkini güncelle
window.addEventListener('scroll', function() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
});

// Ürün arama fonksiyonu (gelecekte eklenebilir)
function searchProducts(query) {
    const filteredProducts = products.filter(product => 
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.description.toLowerCase().includes(query.toLowerCase())
    );
    
    const productsGrid = document.getElementById('productsGrid');
    productsGrid.innerHTML = filteredProducts.map(product => `
        <div class="product-card" data-category="${product.category}">
            <img src="${product.image}" alt="${product.name}" class="product-image" onerror="this.src='images/placeholder.jpg'">
            <div class="product-info">
                <h3 class="product-title">${product.name}</h3>
                <p class="product-description">${product.description}</p>
                <div class="product-price">${product.price}</div>
                <span class="product-category">${getCategoryName(product.category)}</span>
            </div>
        </div>
    `).join('');
}

// LocalStorage'dan ürünleri yükle (admin paneli için)
function loadProductsFromStorage() {
    const storedProducts = localStorage.getItem('marketProducts');
    if (storedProducts) {
        products = JSON.parse(storedProducts);
    }
}

// LocalStorage'a ürünleri kaydet (admin paneli için)
function saveProductsToStorage() {
    localStorage.setItem('marketProducts', JSON.stringify(products));
}

// Sayfa yüklendiğinde localStorage'dan ürünleri yükle
loadProductsFromStorage(); 