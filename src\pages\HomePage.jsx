import { useState } from 'react'
import { useProducts } from '../context/ProductContext'
import FilterPanel from '../components/FilterPanel'
import ProductGrid from '../components/ProductGrid'
import ProductModal from '../components/ProductModal'
import { Loader2 } from 'lucide-react'

const HomePage = () => {
  const { products, loading, error } = useProducts()
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedProduct, setSelectedProduct] = useState(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Filtrelenmiş ürünleri hesapla
  const filteredProducts = products.filter(product => {
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  // Ürün detayını aç
  const openProductDetail = (product) => {
    setSelectedProduct(product)
    setIsModalOpen(true)
  }

  // Modal'ı kapat
  const closeModal = () => {
    setIsModalOpen(false)
    setSelectedProduct(null)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary-500" />
          <span className="text-lg text-gray-600">Ürünler yükleniyor...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 text-lg mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="btn-primary"
          >
            Tekrar Dene
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-primary-500 to-primary-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Modern Alışveriş Deneyimi
          </h1>
          <p className="text-xl md:text-2xl text-primary-100 max-w-2xl mx-auto">
            Kaliteli ürünlerimizi keşfedin, en taze ve güvenilir seçeneklerimizle tanışın.
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filter Panel */}
          <div className="lg:w-80">
            <FilterPanel
              selectedCategory={selectedCategory}
              onCategoryChange={setSelectedCategory}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
            />
          </div>

          {/* Product Grid */}
          <div className="flex-1">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Ürünlerimiz
              </h2>
              <p className="text-gray-600">
                {filteredProducts.length} ürün bulundu
              </p>
            </div>
            
            <ProductGrid 
              products={filteredProducts}
              onProductClick={openProductDetail}
            />
          </div>
        </div>
      </div>

      {/* Product Modal */}
      {selectedProduct && (
        <ProductModal
          product={selectedProduct}
          isOpen={isModalOpen}
          onClose={closeModal}
        />
      )}
    </div>
  )
}

export default HomePage 