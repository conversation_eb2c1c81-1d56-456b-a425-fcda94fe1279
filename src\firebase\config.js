import { initializeApp } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'
import { getStorage } from 'firebase/storage'

// Firebase konfigürasyonu - gerçek projede environment variables kullanın
const firebaseConfig = {
  apiKey: "AIzaSyBXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  authDomain: "market-vitrini.firebaseapp.com",
  projectId: "market-vitrini",
  storageBucket: "market-vitrini.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdefghijklmnop"
}

// Firebase'i başlat
const app = initializeApp(firebaseConfig)

// Servisleri export et
export const auth = getAuth(app)
export const db = getFirestore(app)
export const storage = getStorage(app)

export default app 