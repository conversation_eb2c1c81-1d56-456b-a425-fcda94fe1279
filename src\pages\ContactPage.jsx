import { Mail, Phone, MapPin, Clock } from 'lucide-react'

const ContactPage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-primary-500 to-primary-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            <PERSON><PERSON><PERSON>ş<PERSON>
          </h1>
          <p className="text-xl md:text-2xl text-primary-100 max-w-2xl mx-auto">
            Bizimle iletişime geçin, size yardımcı olmaktan mutluluk duyarız.
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* <PERSON>letişim Bilgileri */}
          <div className="space-y-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                İletişim Bilgileri
              </h2>
              <p className="text-gray-600 text-lg leading-relaxed">
                Müşteri memnuniyeti bizim için en önemli değerdir. 
                Herhangi bir sorunuz veya öneriniz için bizimle iletişime geçebilirsiniz.
              </p>
            </div>

            {/* İletişim Kartları */}
            <div className="space-y-6">
              {/* Adres */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                      <MapPin className="h-6 w-6 text-primary-600" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Adres
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      Mersin Üniversitesi Teknopark Binası<br />
                      Çiftlikköy Mahallesi<br />
                      Yenişehir/Mersin, Türkiye
                    </p>
                  </div>
                </div>
              </div>

              {/* Telefon */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                      <Phone className="h-6 w-6 text-primary-600" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Telefon
                    </h3>
                    <p className="text-gray-600">
                      <a 
                        href="tel:+905551234567" 
                        className="hover:text-primary-600 transition-colors"
                      >
                        +90 555 123 45 67
                      </a>
                    </p>
                  </div>
                </div>
              </div>

              {/* E-posta */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                      <Mail className="h-6 w-6 text-primary-600" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      E-posta
                    </h3>
                    <p className="text-gray-600">
                      <a 
                        href="mailto:<EMAIL>" 
                        className="hover:text-primary-600 transition-colors"
                      >
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>
              </div>

              {/* Çalışma Saatleri */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                      <Clock className="h-6 w-6 text-primary-600" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Çalışma Saatleri
                    </h3>
                    <div className="text-gray-600 space-y-1">
                      <p><strong>Pazartesi - Cuma:</strong> 08:00 - 18:00</p>
                      <p><strong>Cumartesi:</strong> 09:00 - 16:00</p>
                      <p><strong>Pazar:</strong> Kapalı</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Harita */}
          <div className="space-y-6">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Konum
              </h2>
              <p className="text-gray-600 mb-6">
                Mersin Üniversitesi Teknopark Binası'nda bulunan ofisimizi ziyaret edebilirsiniz.
              </p>
            </div>

            {/* Google Maps Embed */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="aspect-video">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3190.1234567890123!2d34.64123456789012!3d36.81234567890123!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzbCsDQ4JzQ0LjQiTiAzNMKwMzgnMjQuNCJF!5e0!3m2!1str!2str!4v1234567890123"
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen=""
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="Market Vitrini Konum"
                />
              </div>
            </div>

            {/* İletişim Formu (Gelecekte eklenebilir) */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Mesaj Gönder
              </h3>
              <p className="text-gray-600 mb-4">
                Bu özellik yakında eklenecek. Şimdilik telefon veya e-posta ile iletişime geçebilirsiniz.
              </p>
              <button 
                className="btn-primary"
                onClick={() => window.open('mailto:<EMAIL>', '_blank')}
              >
                E-posta Gönder
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ContactPage 