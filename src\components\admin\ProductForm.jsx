import { useState, useEffect } from 'react'
import { useProducts } from '../../context/ProductContext'
import { X, Save, Image as ImageIcon } from 'lucide-react'

const ProductForm = ({ product, onClose }) => {
  const { addProduct, updateProduct } = useProducts()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    category: '',
    image: '',
    discount: '',
    inStock: true
  })

  // Form'u ürün verisiyle doldur (düzenleme modunda)
  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || '',
        description: product.description || '',
        price: product.price || '',
        category: product.category || '',
        image: product.image || '',
        discount: product.discount || '',
        inStock: product.inStock !== undefined ? product.inStock : true
      })
    }
  }, [product])

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const productData = {
        ...formData,
        price: parseFloat(formData.price),
        discount: formData.discount ? parseFloat(formData.discount) : 0
      }

      if (product) {
        await updateProduct(product.id, productData)
      } else {
        await addProduct(productData)
      }
      
      onClose()
    } catch (err) {
      setError('Ürün kaydedilirken hata oluştu. Lütfen tekrar deneyin.')
    } finally {
      setLoading(false)
    }
  }

  const categories = [
    { value: 'drinks', label: 'İçecekler' },
    { value: 'snacks', label: 'Atıştırmalıklar' },
    { value: 'breakfast', label: 'Kahvaltılık' },
    { value: 'dairy', label: 'Süt Ürünleri' },
    { value: 'fruits', label: 'Meyve & Sebze' },
    { value: 'meat', label: 'Et & Tavuk' },
    { value: 'bakery', label: 'Fırın Ürünleri' }
  ]

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">
                {product ? 'Ürün Düzenle' : 'Yeni Ürün Ekle'}
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Ürün Adı */}
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ürün Adı *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="input-field"
                    placeholder="Ürün adını girin"
                    disabled={loading}
                  />
                </div>

                {/* Fiyat */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Fiyat (TL) *
                  </label>
                  <input
                    type="number"
                    name="price"
                    value={formData.price}
                    onChange={handleChange}
                    required
                    min="0"
                    step="0.01"
                    className="input-field"
                    placeholder="0.00"
                    disabled={loading}
                  />
                </div>

                {/* Kategori */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Kategori *
                  </label>
                  <select
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    required
                    className="input-field"
                    disabled={loading}
                  >
                    <option value="">Kategori Seçin</option>
                    {categories.map(cat => (
                      <option key={cat.value} value={cat.value}>
                        {cat.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* İndirim */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    İndirim (%)
                  </label>
                  <input
                    type="number"
                    name="discount"
                    value={formData.discount}
                    onChange={handleChange}
                    min="0"
                    max="100"
                    className="input-field"
                    placeholder="0"
                    disabled={loading}
                  />
                </div>

                {/* Stok Durumu */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Stok Durumu
                  </label>
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="inStock"
                        value="true"
                        checked={formData.inStock === true}
                        onChange={handleChange}
                        className="mr-2"
                        disabled={loading}
                      />
                      <span className="text-sm text-gray-700">Stokta Var</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="inStock"
                        value="false"
                        checked={formData.inStock === false}
                        onChange={handleChange}
                        className="mr-2"
                        disabled={loading}
                      />
                      <span className="text-sm text-gray-700">Stokta Yok</span>
                    </label>
                  </div>
                </div>
              </div>

              {/* Görsel URL */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Görsel URL
                </label>
                <div className="relative">
                  <ImageIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="url"
                    name="image"
                    value={formData.image}
                    onChange={handleChange}
                    className="input-field pl-10"
                    placeholder="https://example.com/image.jpg"
                    disabled={loading}
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Ürün görselinin URL adresini girin
                </p>
              </div>

              {/* Açıklama */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Açıklama *
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  required
                  rows="4"
                  className="input-field"
                  placeholder="Ürün açıklamasını girin"
                  disabled={loading}
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="btn-secondary"
                  disabled={loading}
                >
                  İptal
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="btn-primary flex items-center space-x-2"
                >
                  <Save className="h-4 w-4" />
                  <span>{loading ? 'Kaydediliyor...' : (product ? 'Güncelle' : 'Kaydet')}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductForm 