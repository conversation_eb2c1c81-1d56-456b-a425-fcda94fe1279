import { useEffect } from 'react'
import { X, Tag, Package } from 'lucide-react'

const ProductModal = ({ product, isOpen, onClose }) => {
  const { name, description, price, image, discount, category, inStock } = product

  // Kategori adını Türkçe'ye çevir
  const getCategoryName = (category) => {
    const categoryNames = {
      'drinks': 'İçecekler',
      'snacks': 'Atıştırmalıklar',
      'breakfast': 'Kahvaltılık',
      'dairy': 'Süt Ürünleri',
      'fruits': 'Meyve & Sebze',
      'meat': 'Et & Tavuk',
      'bakery': 'Fırın Ürünleri'
    }
    return categoryNames[category] || category
  }

  // İndirimli fiyatı hesapla
  const discountedPrice = discount ? price * (1 - discount / 100) : price

  // ESC tuşu ile modal'ı kapat
  useEffect(() => {
    const handleEsc = (event) => {
      if (event.keyCode === 27) onClose()
    }
    window.addEventListener('keydown', handleEsc)
    return () => window.removeEventListener('keydown', handleEsc)
  }, [onClose])

  // Modal açıkken body scroll'unu engelle
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Ürün Detayı
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Ürün Görseli */}
              <div className="relative">
                <img
                  src={image || '/placeholder-product.jpg'}
                  alt={name}
                  className="w-full h-64 md:h-80 object-cover rounded-lg"
                  onError={(e) => {
                    e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjhGOUZBIi8+CjxwYXRoIGQ9Ik0xNTAgMTUwQzE2Ni41NjkgMTUwIDE4MCAxMzYuNTY5IDE4MCAxMjBDMTgwIDEwMy40MzEgMTY2LjU2OSA5MCAxNTAgOTBDMTMzLjQzMSA5MCAxMjAgMTAzLjQzMSAxMjAgMTIwQzEyMCAxMzYuNTY5IDEzMy40MzEgMTUwIDE1MCAxNTBaIiBmaWxsPSIjQzRDNUM3Ii8+CjxwYXRoIGQ9Ik0xNTAgMTgwQzE2Ni41NjkgMTgwIDE4MCAxNjYuNTY5IDE4MCAxNTBDMTgwIDEzMy40MzEgMTY2LjU2OSAxMjAgMTUwIDEyMEMxMzMuNDMxIDEyMCAxMjAgMTMzLjQzMSAxMjAgMTUwQzEyMCAxNjYuNTY5IDEzMy40MzEgMTgwIDE1MCAxODBaIiBmaWxsPSIjQzRDNUM3Ii8+CjxwYXRoIGQ9Ik0xNTAgMjEwQzE2Ni41NjkgMjEwIDE4MCAxOTYuNTY5IDE4MCAxODBDMTgwIDE2My40MzEgMTY2LjU2OSAxNTAgMTUwIDE1MEMxMzMuNDMxIDE1MCAxMjAgMTYzLjQzMSAxMjAgMTgwQzEyMCAxOTYuNTY5IDEzMy40MzEgMjEwIDE1MCAyMTBaIiBmaWxsPSIjQzRDNUM3Ii8+Cjwvc3ZnPgo='
                  }}
                />
                
                {/* İndirim Badge */}
                {discount && (
                  <div className="absolute top-2 left-2 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                    %{discount} İndirim
                  </div>
                )}
                
                {/* Stok Durumu */}
                <div className="absolute top-2 right-2">
                  <span className={`px-3 py-1 rounded-full text-sm font-bold ${
                    inStock 
                      ? 'bg-green-500 text-white' 
                      : 'bg-gray-500 text-white'
                  }`}>
                    {inStock ? 'Stokta Var' : 'Stokta Yok'}
                  </span>
                </div>
              </div>

              {/* Ürün Bilgileri */}
              <div className="space-y-4">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    {name}
                  </h2>
                  
                  <div className="flex items-center space-x-2 mb-3">
                    <Tag className="h-4 w-4 text-primary-500" />
                    <span className="text-sm text-gray-600">
                      {getCategoryName(category)}
                    </span>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Açıklama</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {description}
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Fiyat</h4>
                  <div className="flex items-center space-x-3">
                    {discount ? (
                      <>
                        <span className="text-3xl font-bold text-primary-600">
                          {discountedPrice.toFixed(2)} TL
                        </span>
                        <span className="text-lg text-gray-500 line-through">
                          {price.toFixed(2)} TL
                        </span>
                        <span className="bg-red-100 text-red-600 px-2 py-1 rounded text-sm font-medium">
                          %{discount} Tasarruf
                        </span>
                      </>
                    ) : (
                      <span className="text-3xl font-bold text-primary-600">
                        {price.toFixed(2)} TL
                      </span>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Stok Durumu</h4>
                  <div className="flex items-center space-x-2">
                    <Package className={`h-4 w-4 ${
                      inStock ? 'text-green-500' : 'text-gray-500'
                    }`} />
                    <span className={`text-sm font-medium ${
                      inStock ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {inStock ? 'Ürün stokta mevcut' : 'Ürün stokta bulunmuyor'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              onClick={onClose}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-500 text-base font-medium text-white hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Kapat
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductModal 