import { useState } from 'react'
import { useProducts } from '../../context/ProductContext'
import { Edit, Trash2, Eye, Package } from 'lucide-react'

const ProductTable = ({ products, onEdit }) => {
  const { deleteProduct } = useProducts()
  const [deletingId, setDeletingId] = useState(null)

  // Kategori adını Türkçe'ye çevir
  const getCategoryName = (category) => {
    const categoryNames = {
      'drinks': 'İçecekler',
      'snacks': 'Atıştırmalıklar',
      'breakfast': 'Kahvaltılık',
      'dairy': 'Süt Ürünleri',
      'fruits': 'Meyve & Sebze',
      'meat': 'Et & Tavuk',
      'bakery': 'Fırın Ürünleri'
    }
    return categoryNames[category] || category
  }

  // Ürün silme işlemi
  const handleDelete = async (productId) => {
    if (window.confirm('Bu <PERSON>r<PERSON><PERSON><PERSON> silmek istediğinizden emin misiniz?')) {
      setDeletingId(productId)
      try {
        await deleteProduct(productId)
      } catch (error) {
        console.error('Delete error:', error)
      } finally {
        setDeletingId(null)
      }
    }
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz ürün yok</h3>
        <p className="text-gray-500">İlk ürününüzü eklemek için "Yeni Ürün Ekle" butonunu kullanın.</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Görsel
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Ürün Adı
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Kategori
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Fiyat
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Stok
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              İşlemler
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {products.map((product) => (
            <tr key={product.id} className="hover:bg-gray-50">
              {/* Görsel */}
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex-shrink-0 h-12 w-12">
                  <img
                    src={product.image || '/placeholder-product.jpg'}
                    alt={product.name}
                    className="h-12 w-12 rounded-lg object-cover"
                    onError={(e) => {
                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjhGOUZBIi8+CjxwYXRoIGQ9Ik0yNCAyNEMyOC40MTgzIDI0IDMyIDIwLjQxODMgMzIgMTZDMzIgMTEuNTgxNyAyOC40MTgzIDggMjQgOEMxOS41ODE3IDggMTYgMTEuNTgxNyAxNiAxNkMxNiAyMC40MTgzIDE5LjU4MTcgMjQgMjQgMjRaIiBmaWxsPSIjQzRDNUM3Ii8+CjxwYXRoIGQ9Ik0yNCAyOEMyOC40MTgzIDI4IDMyIDI0LjQxODMgMzIgMjBDMzIgMTUuNTgxNyAyOC40MTgzIDEyIDI0IDEyQzE5LjU4MTcgMTIgMTYgMTUuNTgxNyAxNiAyMEMxNiAyNC40MTgzIDE5LjU4MTcgMjggMjQgMjhaIiBmaWxsPSIjQzRDNUM3Ii8+CjxwYXRoIGQ9Ik0yNCAzMkMyOC40MTgzIDMyIDMyIDI4LjQxODMgMzIgMjRDMzIgMTkuNTgxNyAyOC40MTgzIDE2IDI0IDE2QzE5LjU4MTcgMTYgMTYgMTkuNTgxNyAxNiAyNEMxNiAyOC40MTgzIDE5LjU4MTcgMzIgMjQgMzJaIiBmaWxsPSIjQzRDNUM3Ii8+Cjwvc3ZnPgo='
                    }}
                  />
                </div>
              </td>

              {/* Ürün Adı */}
              <td className="px-6 py-4 whitespace-nowrap">
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {product.name}
                  </div>
                  <div className="text-sm text-gray-500 truncate max-w-xs">
                    {product.description}
                  </div>
                </div>
              </td>

              {/* Kategori */}
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                  {getCategoryName(product.category)}
                </span>
              </td>

              {/* Fiyat */}
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">
                  {product.price.toFixed(2)} TL
                </div>
                {product.discount > 0 && (
                  <div className="text-xs text-red-600">
                    %{product.discount} indirim
                  </div>
                )}
              </td>

              {/* Stok Durumu */}
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  product.inStock
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {product.inStock ? 'Stokta' : 'Stokta Yok'}
                </span>
              </td>

              {/* İşlemler */}
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => onEdit(product)}
                    className="text-primary-600 hover:text-primary-900 transition-colors"
                    title="Düzenle"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  
                  <button
                    onClick={() => handleDelete(product.id)}
                    disabled={deletingId === product.id}
                    className="text-red-600 hover:text-red-900 transition-colors disabled:opacity-50"
                    title="Sil"
                  >
                    {deletingId === product.id ? (
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent" />
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default ProductTable 