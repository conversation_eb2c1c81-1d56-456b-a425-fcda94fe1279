/* Reset ve <PERSON><PERSON> */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header ve Navbar */
.header {
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: bold;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 5px;
}

.nav-link:hover,
.nav-link.active {
    color: #3498db;
    background-color: #f8f9fa;
}

.admin-link {
    background-color: #e74c3c;
    color: white !important;
    padding: 0.5rem 1rem;
    border-radius: 5px;
}

.admin-link:hover {
    background-color: #c0392b;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Bölümü */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
}

.hero-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    display: inline-block;
    background-color: #fff;
    color: #667eea;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 50px;
    font-weight: bold;
    transition: transform 0.3s ease;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

/* Bölüm Başlıkları */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* Kategoriler */
.categories {
    padding: 80px 0;
    background-color: #fff;
}

.category-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 3rem;
}

.filter-btn {
    padding: 0.8rem 1.5rem;
    border: 2px solid #3498db;
    background: transparent;
    color: #3498db;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: #3498db;
    color: white;
}

/* Ürünler */
.products {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.2);
}

.product-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    background-color: #f8f9fa;
}

.product-info {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-title {
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 0.8rem;
    color: #2c3e50;
    line-height: 1.3;
}

.product-description {
    color: #666;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
    line-height: 1.5;
    flex: 1;
}

.product-price {
    font-size: 1.4rem;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 0.8rem;
}

.product-category {
    display: inline-block;
    background-color: #3498db;
    color: white;
    padding: 0.4rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    align-self: flex-start;
}

/* Hakkımızda */
.about {
    padding: 80px 0;
    background-color: #fff;
}

.about-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.about-content p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 3rem;
    line-height: 1.8;
}

.about-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature {
    text-align: center;
    padding: 2rem;
    background-color: #f8f9fa;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.feature:hover {
    transform: translateY(-5px);
}

.feature i {
    font-size: 3rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.feature h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.feature p {
    color: #666;
}

/* İletişim */
.contact {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.contact-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    max-width: 1000px;
    margin: 0 auto;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.contact-item i {
    font-size: 1.5rem;
    color: #3498db;
    margin-top: 0.2rem;
}

.contact-item h3 {
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.contact-item p {
    color: #666;
    line-height: 1.6;
}

.contact-hours {
    background-color: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: fit-content;
}

.contact-hours h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.contact-hours p {
    margin-bottom: 0.5rem;
    color: #666;
}

/* Footer */
.footer {
    background-color: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: #3498db;
}

.footer-section p {
    color: #bdc3c7;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: #3498db;
    color: white;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.social-links a:hover {
    background-color: #2980b9;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #3498db;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Responsive Tasarım */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .about-features {
        grid-template-columns: 1fr;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .product-card {
        margin-bottom: 1rem;
    }

    .product-image {
        height: 200px;
    }

    .product-info {
        padding: 1rem;
    }

    .product-title {
        font-size: 1.1rem;
    }

    .product-description {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-content h1 {
        font-size: 1.8rem;
    }

    .category-filters {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
    }

    .filter-btn {
        width: 200px;
        padding: 0.6rem 1rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .product-card {
        margin-bottom: 0.5rem;
    }

    .product-image {
        height: 180px;
    }

    .product-info {
        padding: 0.8rem;
    }

    .product-title {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .product-description {
        font-size: 0.85rem;
        margin-bottom: 1rem;
    }

    .product-price {
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
    }

    .product-category {
        font-size: 0.8rem;
        padding: 0.3rem 0.8rem;
    }

    .section-title {
        font-size: 1.8rem;
        margin-bottom: 2rem;
    }
} 