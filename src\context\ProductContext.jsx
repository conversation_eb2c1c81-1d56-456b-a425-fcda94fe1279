import { createContext, useContext, useState, useEffect } from 'react'
import { 
  collection, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc,
  query,
  where,
  orderBy 
} from 'firebase/firestore'
import { db } from '../firebase/config'

const ProductContext = createContext()

export const useProducts = () => {
  const context = useContext(ProductContext)
  if (!context) {
    throw new Error('useProducts must be used within a ProductProvider')
  }
  return context
}

export const ProductProvider = ({ children }) => {
  const [products, setProducts] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Ürünleri getir
  const fetchProducts = async () => {
    try {
      setLoading(true)
      const q = query(collection(db, 'products'), orderBy('createdAt', 'desc'))
      const querySnapshot = await getDocs(q)
      const productsData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }))
      setProducts(productsData)
    } catch (err) {
      setError('Ürünler yüklenirken hata oluştu')
      console.error('Error fetching products:', err)
    } finally {
      setLoading(false)
    }
  }

  // Yeni ürün ekle
  const addProduct = async (productData) => {
    try {
      const docRef = await addDoc(collection(db, 'products'), {
        ...productData,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      await fetchProducts()
      return docRef.id
    } catch (err) {
      setError('Ürün eklenirken hata oluştu')
      throw err
    }
  }

  // Ürün güncelle
  const updateProduct = async (id, productData) => {
    try {
      const productRef = doc(db, 'products', id)
      await updateDoc(productRef, {
        ...productData,
        updatedAt: new Date()
      })
      await fetchProducts()
    } catch (err) {
      setError('Ürün güncellenirken hata oluştu')
      throw err
    }
  }

  // Ürün sil
  const deleteProduct = async (id) => {
    try {
      await deleteDoc(doc(db, 'products', id))
      await fetchProducts()
    } catch (err) {
      setError('Ürün silinirken hata oluştu')
      throw err
    }
  }

  // Kategoriye göre filtrele
  const getProductsByCategory = (category) => {
    if (category === 'all') return products
    return products.filter(product => product.category === category)
  }

  // Arama yap
  const searchProducts = (searchTerm) => {
    if (!searchTerm.trim()) return products
    const term = searchTerm.toLowerCase()
    return products.filter(product => 
      product.name.toLowerCase().includes(term) ||
      product.description.toLowerCase().includes(term)
    )
  }

  useEffect(() => {
    fetchProducts()
  }, [])

  const value = {
    products,
    loading,
    error,
    fetchProducts,
    addProduct,
    updateProduct,
    deleteProduct,
    getProductsByCategory,
    searchProducts
  }

  return (
    <ProductContext.Provider value={value}>
      {children}
    </ProductContext.Provider>
  )
} 